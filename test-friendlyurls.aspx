<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>FriendlyUrls状态检测</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .enabled { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .disabled { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-link { display: block; margin: 10px 0; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <h1>FriendlyUrls状态检测</h1>
    
    <h2>当前页面信息</h2>
    <div class="test-link">
        <strong>当前URL:</strong> <span id="currentUrl"></span><br>
        <strong>是否包含.aspx:</strong> <span id="hasAspx"></span>
    </div>

    <h2>测试链接</h2>
    <p>点击以下链接测试FriendlyUrls是否工作：</p>
    
    <a href="Flow/CustomerTrans.aspx" class="test-link" target="_blank">
        传统格式: Flow/CustomerTrans.aspx
    </a>
    
    <a href="Flow/CustomerTrans" class="test-link" target="_blank">
        FriendlyUrls格式: Flow/CustomerTrans
    </a>

    <h2>检测结果</h2>
    <div id="friendlyUrlsStatus" class="status">
        检测中...
    </div>

    <h2>服务器端检测</h2>
    <div class="test-link">
        <% 
        try 
        {
            // 检查RouteTable是否包含FriendlyUrls路由
            bool hasFriendlyUrls = System.Web.Routing.RouteTable.Routes.Count > 0;
            string routeInfo = "";
            
            foreach(System.Web.Routing.RouteBase route in System.Web.Routing.RouteTable.Routes)
            {
                routeInfo += route.GetType().Name + "; ";
            }
            
            Response.Write("<strong>路由表状态:</strong> " + (hasFriendlyUrls ? "已配置" : "未配置") + "<br>");
            Response.Write("<strong>路由数量:</strong> " + System.Web.Routing.RouteTable.Routes.Count + "<br>");
            Response.Write("<strong>路由类型:</strong> " + routeInfo + "<br>");
            
            // 检查是否有FriendlyUrls相关的路由
            bool hasFriendlyUrlsRoute = routeInfo.Contains("FriendlyUrlRoute");
            Response.Write("<strong>FriendlyUrls路由:</strong> " + (hasFriendlyUrlsRoute ? "已启用" : "未启用") + "<br>");
        }
        catch(Exception ex)
        {
            Response.Write("<strong>检测错误:</strong> " + ex.Message);
        }
        %>
    </div>

    <script>
        // 客户端检测
        document.addEventListener('DOMContentLoaded', function() {
            var currentUrl = window.location.href;
            var hasAspx = currentUrl.indexOf('.aspx') > -1;
            
            document.getElementById('currentUrl').textContent = currentUrl;
            document.getElementById('hasAspx').textContent = hasAspx ? '是' : '否';
            
            var statusDiv = document.getElementById('friendlyUrlsStatus');
            
            // 测试是否可以访问无扩展名的URL
            testFriendlyUrls(function(isEnabled) {
                if (isEnabled) {
                    statusDiv.className = 'status enabled';
                    statusDiv.innerHTML = '✓ FriendlyUrls已启用 - 可以使用无扩展名URL';
                } else {
                    statusDiv.className = 'status disabled';
                    statusDiv.innerHTML = '✗ FriendlyUrls未启用或配置有问题';
                }
            });
        });

        function testFriendlyUrls(callback) {
            // 创建一个隐藏的iframe来测试无扩展名URL是否可访问
            var iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'Flow/CustomerTrans'; // 无扩展名
            
            var timeout = setTimeout(function() {
                document.body.removeChild(iframe);
                callback(false); // 超时认为未启用
            }, 3000);
            
            iframe.onload = function() {
                clearTimeout(timeout);
                try {
                    // 如果能加载说明FriendlyUrls工作
                    document.body.removeChild(iframe);
                    callback(true);
                } catch(e) {
                    document.body.removeChild(iframe);
                    callback(false);
                }
            };
            
            iframe.onerror = function() {
                clearTimeout(timeout);
                document.body.removeChild(iframe);
                callback(false);
            };
            
            document.body.appendChild(iframe);
        }
    </script>
</body>
</html>
