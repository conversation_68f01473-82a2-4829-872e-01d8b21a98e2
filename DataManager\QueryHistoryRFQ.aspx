﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="QueryHistoryRFQ.aspx.cs" Inherits="WebApplication1.DataManager.QueryHistoryRFQ" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>RFQ历史记录查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <style>
        .table-container {
        width: 100%;
        overflow-x: auto;
        overflow-y: auto;
    }
        /* 固定行高，确保对齐 */
        .layui-table-cell {
            height: auto !important;
            line-height: 20px;
            padding: 5px 10px;
            white-space: normal;
        }
        /* 产品信息容器样式 */
        .product-info {
            max-height: none;
            padding: 0;
        }
        .product-info p {
            margin: 0;
            line-height: 20px;
            white-space: nowrap;
        }
        /* 确保滚动条不影响布局 */
        .layui-table-body::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        .layui-table-body::-webkit-scrollbar-thumb {
            background-color: #c1c1c1;
            border-radius: 4px;
        }
        /* 固定列样式调整 */
        .layui-table-fixed {
            height: 100% !important;
        }
        .layui-table-fixed .layui-table-body {
            height: 100% !important;
        }
    </style>
</head>
<body>
    <div class="layui-container">
        <!-- 搜索条件 -->
        <div class="layui-card">
            <div class="layui-card-header">查询条件</div>
            <div class="layui-card-body">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">RFQ编号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="rfqNo" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">客户名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="customerName" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" lay-submit lay-filter="searchForm">查询</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="table-container">
        <!-- 数据表格 -->
        <table id="rfqTable" lay-filter="rfqTable"></table>
        </div>
    </div>

    <!-- 状态模板 -->
    <script type="text/html" id="statusTpl">
        {{#  if(d.Status == 1){ }}
            <a class="layui-btn layui-btn-xs" layuimini-content-href="../Flow/CustomerTrans?RFQNo={{d.RFQNo}}" data-title="待导入原始BOM - {{d.RFQNo}}">待导入原始BOM</a>
        {{#  } else if(d.Status == 2){ }}
            <a class="layui-btn layui-btn-xs layui-btn-normal" layuimini-content-href="../Flow/ConfirmPart?RFQNo={{d.RFQNo}}" data-title="共用料确认中 - {{d.RFQNo}}">共用料确认中</a>
        {{#  } else if(d.Status == 3){ }}
            <a class="layui-btn layui-btn-xs layui-btn-normal" layuimini-content-href="../Flow/SendQuote?RFQNo={{d.RFQNo}}" data-title="发送报价中 - {{d.RFQNo}}">发送报价中</a>
        {{#  } else if(d.Status == 4){ }}
            <a class="layui-btn layui-btn-xs layui-btn-normal" layuimini-content-href="../DataManager/QueryQuote" data-title="待供应商报价中">待供应商报价中</a>
        {{#  } else if(d.Status == 5){ }}
            <a class="layui-btn layui-btn-xs layui-btn-normal" layuimini-content-href="../AssignFlow/FlowApply?RFQNo={{d.RFQNo}}" data-title="待启动签核中 - {{d.RFQNo}}">待启动签核中</a>
        {{#  } else if(d.Status == 6){ }}
            <a class="layui-btn layui-btn-xs layui-btn-warm" layuimini-content-href="../AssignFlow/FlowAssign" data-title="签核流程中">签核流程中</a>
        {{#  } else if(d.Status == 7){ }}
            <a class="layui-btn layui-btn-xs layui-btn-disabled">已完成</a>
        {{#  } }}
    </script>

    <!-- 产品型号模板 -->
     <script type="text/html" id="productTpl">
        <div class="product-info">
            {{# if(d.products && d.products.length > 0){ }}
                {{# layui.each(d.products, function(index, item){ }}
                    <p><b>型号：</b>{{item.model}} <b>EAU1：</b>{{item.eau1}} <b>EAU2：</b>{{item.eau2}} <b>EAU3：</b>{{item.eau3}}</p> 
                {{# }); }}
            {{# } else { }}
                <p>暂无产品信息</p>
            {{# } }}
        </div>
    </script>
    <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script src="../js/navigate.js" charset="utf-8"></script>
    <script>
        layui.use(['table', 'form', 'layer'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;
            // 表格实例
            table.render({
                elem: '#rfqTable'
                ,url: '../ashx/HistoryControl.ashx/GetHistorylist' //后台数据接口
                , height: 650
                , page: false
                , limit: 10000
                , size: 'lg'  // 使用大尺寸
                ,cols: [[ //表头
                    { field: 'RFQNo', title: 'RFQ编号', width: 200, style: 'height:auto;'}
                    ,{field: 'createDate', title: '创建日期', width: 120, sort: true}
                    , { field: 'Customer', title: '客户名称', width: 150}
                    , { field: 'ProjectApp', title: '项目名称', width: 150}
                    , { title: '产品信息', width: 320, templet: '#productTpl', style: 'height:auto;' }
                    ,{field: 'endDate', title: '结束日期', width: 120, sort: true}
                    , { field: 'Status', title: 'RFQ状态', width: 150, templet: '#statusTpl'}
                ]]
                , done: function () {
                    // 强制重新计算表格高度
                    this.elem.next().find('.layui-table-box').css('height', 'auto');
                    // 确保固定列高度正确
                    layui.$('.layui-table-fixed').css('height', layui.$('.layui-table-main').height());
                }
            });

           

            // 监听搜索表单提交
            form.on('submit(searchForm)', function(data){
                table.reload('rfqTable', {
                    where: data.field

                });
                return false;
            });

            // 添加layuimini-content-href事件监听器
            $(document).on('click', '[layuimini-content-href]', function(e) {
                e.preventDefault();
                var href = $(this).attr('layuimini-content-href');
                var title = $(this).attr('data-title') || '新标签页';

                // 检查是否在iframe环境中
                if (window.parent && window.parent !== window && typeof navigateInNewTab === 'function') {
                    // 在iframe中，使用navigateInNewTab函数在新标签页中打开
                    navigateInNewTab(href, title);
                } else {
                    // 不在iframe中或函数不可用，直接跳转
                    window.location.href = href;
                }
            });
        });
    </script>
</body>
</html>
