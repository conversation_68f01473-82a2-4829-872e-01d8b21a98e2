﻿// 定义导航函数
function navigateInIframe(url, title) {
    try {
        // 确保URL使用FriendlyUrls格式（自动去掉.aspx扩展名）
        var cleanUrl = url;
        if (cleanUrl.indexOf('.aspx') > -1) {
            cleanUrl = cleanUrl.replace('.aspx', '');
            console.log('转换为FriendlyUrls格式:', cleanUrl);
        }

        // 获取当前iframe索引
        var currentFrame = window.frameElement;
        var parentDocument = window.parent.document;

        // 如果是在tab中，尝试更新tab的标题和URL
        if (window.parent.layui) {
            var tabTitle = parentDocument.querySelector('.layui-tab-title');
            if (tabTitle) {
                var activeLi = tabTitle.querySelector('.layui-this');
                if (activeLi) {
                    // 更新标题
                    activeLi.innerText = title;
                    // 更新URL属性
                    activeLi.setAttribute('lay-attr', cleanUrl);

                    // 更新iframe的src
                    var iframeId = activeLi.getAttribute('lay-id');
                    if (iframeId) {
                        var targetIframe = parentDocument.querySelector('iframe[data-id="' + iframeId + '"]');
                        if (targetIframe) {
                            targetIframe.src = cleanUrl;
                            return;
                        }
                    }
                }
            }
        }

        // 如果上面的方法都失败了，直接修改当前iframe的src
        currentFrame.src = cleanUrl;

    } catch (e) {
        console.error('Navigation error:', e);
        // 最后的备选方案：直接跳转
        window.location.href = cleanUrl;
    }
}

// 使用layuimini框架原生方法在新标签页中打开链接
function navigateInNewTab(url, title) {
    console.log('navigateInNewTab 被调用:', url, title);

    try {
        // 方法1：使用layuimini框架的openNewTabByIframe方法（推荐）
        if (window.parent.layui && window.parent.layui.miniTab) {
            console.log('找到 layui.miniTab，使用openNewTabByIframe方法');

            // 确保URL使用FriendlyUrls格式
            var cleanUrl = url;
            if (cleanUrl.indexOf('.aspx') > -1) {
                cleanUrl = cleanUrl.replace('.aspx', '');
                console.log('转换为FriendlyUrls格式:', cleanUrl);
            }

            // 使用miniTab的openNewTabByIframe方法
            window.parent.layui.miniTab.openNewTabByIframe({
                href: cleanUrl,
                title: title || '新标签页'
            });

            console.log('成功使用openNewTabByIframe方法打开新标签页');
            return true;
        }

        // 方法2：使用layuimini-content-href属性触发方式（备选方案）
        if (window.parent.$ && window.parent.layui) {
            console.log('尝试使用layuimini-content-href属性方式');

            // 创建一个临时的链接元素来触发框架的事件处理
            var tempLink = window.parent.$('<a></a>');
            tempLink.attr('layuimini-content-href', url);
            tempLink.attr('data-title', title || '新标签页');

            // 将临时元素添加到页面中（隐藏）
            tempLink.css('display', 'none');
            window.parent.$('body').append(tempLink);

            // 触发点击事件，这会调用layuimini框架的原生处理逻辑
            tempLink.trigger('click');

            // 移除临时元素
            tempLink.remove();

            console.log('成功使用layuimini-content-href方式打开');
            return true;
        }

        // 方法3：直接使用layui element的tabAdd方法（最后备选方案）
        if (window.parent.layui && window.parent.layui.element) {
            console.log('尝试使用 layui.element.tabAdd 方法');
            var element = window.parent.layui.element;

            // 确保URL使用FriendlyUrls格式
            var cleanUrl = url;
            if (cleanUrl.indexOf('.aspx') > -1) {
                cleanUrl = cleanUrl.replace('.aspx', '');
            }

            // 使用URL作为标签页ID
            var tabId = cleanUrl;

            // 添加新的标签页
            element.tabAdd('layuiminiTab', {
                title: '<span class="layuimini-tab-active"></span><span>' + (title || '新标签页') + '</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>',
                content: '<iframe width="100%" height="100%" frameborder="no" border="0" marginwidth="0" marginheight="0" src="' + cleanUrl + '"></iframe>',
                id: tabId
            });

            // 切换到新标签页
            element.tabChange('layuiminiTab', tabId);
            console.log('成功使用element.tabAdd创建新标签页:', tabId);
            return true;
        }

        // 备选方案：在新窗口中打开
        console.log('使用新窗口打开作为备选方案');
        window.open(url, '_blank');
        return true;

    } catch (e) {
        console.error('打开新标签页失败:', e);

        // 错误时的备选方案：在新窗口中打开
        try {
            console.log('错误处理：使用新窗口打开');
            window.open(url, '_blank');
            return true;
        } catch (openError) {
            console.error('打开新窗口也失败:', openError);
            alert('无法打开新页面，请检查浏览器设置');
            return false;
        }
    }
}

// 添加一个用于调试的函数，可以帮助我们了解当前iframe的具体环境
function debugIframeInfo() {
    console.log('Current window location:', window.location.href);
    console.log('Frame element:', window.frameElement);
    if (window.frameElement) {
        console.log('Frame ID:', window.frameElement.id);
        console.log('Frame name:', window.frameElement.name);
        console.log('Frame attributes:', window.frameElement.attributes);
    }
    console.log('Parent layui available:', !!window.parent.layui);
    console.log('Parent element available:', !!window.parent.element);
}