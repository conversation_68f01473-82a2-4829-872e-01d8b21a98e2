<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>导航函数测试</title>
    <script src="js/navigate.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .url-info { background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>导航函数测试页面</h1>
    
    <div class="url-info">
        <strong>当前页面URL:</strong> <span id="currentUrl"></span><br>
        <strong>检测到的服务器类型:</strong> <span id="serverType"></span>
    </div>

    <div class="test-section">
        <h3>URL转换测试</h3>
        <p>测试getCompatibleUrl函数的URL转换功能：</p>
        <button class="test-button" onclick="testUrlConversion('../Flow/CustomerTrans.aspx?RFQNo=TEST001')">
            测试: ../Flow/CustomerTrans.aspx?RFQNo=TEST001
        </button>
        <button class="test-button" onclick="testUrlConversion('../Flow/CustomerTrans?RFQNo=TEST001')">
            测试: ../Flow/CustomerTrans?RFQNo=TEST001
        </button>
        <button class="test-button" onclick="testUrlConversion('../Flow/CreateRfq.aspx')">
            测试: ../Flow/CreateRfq.aspx
        </button>
        <button class="test-button" onclick="testUrlConversion('../Flow/CreateRfq')">
            测试: ../Flow/CreateRfq
        </button>
    </div>

    <div class="test-section">
        <h3>导航函数测试</h3>
        <p>测试navigateInIframe函数（注意：这些测试可能会改变当前页面）：</p>
        <button class="test-button" onclick="testNavigateInIframe('../Flow/CustomerTrans.aspx?RFQNo=TEST001', '客户模板转换-TEST001')">
            测试导航到CustomerTrans.aspx
        </button>
        <button class="test-button" onclick="testNavigateInIframe('../Flow/CreateRfq.aspx?RFQNo=TEST001', '新建RFQ流程-TEST001')">
            测试导航到CreateRfq.aspx
        </button>
    </div>

    <div class="test-section">
        <h3>调试信息</h3>
        <button class="test-button" onclick="showDebugInfo()">显示调试信息</button>
        <button class="test-button" onclick="clearLog()">清除日志</button>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <div id="logArea" class="log-area"></div>
    </div>

    <script>
        // 重写console.log以显示在页面上
        var originalLog = console.log;
        console.log = function() {
            originalLog.apply(console, arguments);
            var logArea = document.getElementById('logArea');
            var message = Array.prototype.slice.call(arguments).join(' ');
            var timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += '[' + timestamp + '] ' + message + '\n';
            logArea.scrollTop = logArea.scrollHeight;
        };

        // 页面加载时显示当前URL信息
        document.addEventListener('DOMContentLoaded', function() {
            var currentUrl = window.location.href;
            var serverSupportsFriendlyUrls = currentUrl.indexOf('.aspx') === -1;
            
            document.getElementById('currentUrl').textContent = currentUrl;
            document.getElementById('serverType').textContent = serverSupportsFriendlyUrls ? 'FriendlyUrls服务器' : '传统ASP.NET服务器';
            
            console.log('页面加载完成');
            console.log('当前URL:', currentUrl);
            console.log('服务器类型:', serverSupportsFriendlyUrls ? 'FriendlyUrls' : '传统ASP.NET');
        });

        function testUrlConversion(url) {
            console.log('=== URL转换测试 ===');
            console.log('输入URL:', url);
            
            if (typeof getCompatibleUrl === 'function') {
                var result = getCompatibleUrl(url);
                console.log('转换结果:', result);
                console.log('转换成功: ' + (result !== url ? '是' : '否'));
            } else {
                console.log('错误: getCompatibleUrl函数未找到');
            }
            console.log('');
        }

        function testNavigateInIframe(url, title) {
            console.log('=== 导航测试 ===');
            console.log('目标URL:', url);
            console.log('标题:', title);
            
            if (typeof navigateInIframe === 'function') {
                try {
                    navigateInIframe(url, title);
                    console.log('导航函数调用成功');
                } catch (e) {
                    console.log('导航函数调用失败:', e.message);
                }
            } else {
                console.log('错误: navigateInIframe函数未找到');
            }
            console.log('');
        }

        function showDebugInfo() {
            console.log('=== 调试信息 ===');
            if (typeof debugIframeInfo === 'function') {
                debugIframeInfo();
            } else {
                console.log('debugIframeInfo函数未找到');
            }
            
            console.log('window.frameElement:', window.frameElement);
            console.log('window.parent === window:', window.parent === window);
            console.log('window.parent.layui:', typeof window.parent.layui);
            console.log('');
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }
    </script>
</body>
</html>
